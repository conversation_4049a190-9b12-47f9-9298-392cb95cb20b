import React, { useRef, useEffect, useState, useMemo } from "react";
// @ts-ignore
import videojs from "video.js";
import "video.js/dist/video-js.css";

interface IProps {
  videos: {
    src: string;
    duration: number;
    endTimeSeconds: number;
    startTimeSeconds: number;
  }[];
  // 是否启用按照传入的时间点播放
  useTimeSecond?: boolean;
}
export const VideojsPlayer: React.FC<IProps> = (props: IProps) => {
  const { videos, useTimeSecond = false } = props;
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);
  console.log(videos, "videos");
  // 状态管理
  const [currentTime, setCurrentTime] = useState("0:10");
  const [totalDuration, setTotalDuration] = useState("0:36");
  const [progress, setProgress] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(100);
  const [isMuted, setIsMuted] = useState(false);
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [accumulatedTime, setAccumulatedTime] = useState(0);

  //   // 视频配置
  //   const videos = useMemo(() => [
  //     { src: 'http://vjs.zencdn.net/v/oceans.mp4', duration: 36 },
  //     { src: 'https://media.w3.org/2010/05/sintel/trailer.mp4', duration: 52 }
  //   ], []);

  // 计算总时长
  const totalDurationSeconds = useMemo(
    () => videos.reduce((sum, video) => sum + video.duration, 0),
    [videos]
  );

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // 初始化播放器
  useEffect(() => {
    if (!videoRef.current) return;

    const player = videojs(videoRef.current, {
      controls: false,
      fluid: false,
      responsive: false,
      preload: "metadata",
      playsinline: true,
      sources: [
        {
          src: videos[0].src,
          type: "video/mp4",
        },
      ],
    });

    playerRef.current = player;

    // 设置总时长显示
    setTotalDuration(formatTime(totalDurationSeconds));

    // 播放器就绪后的处理
    player.ready(() => {
      // 隐藏原生控制条
      const controlBar = document.querySelector(".vjs-control-bar");
      if (controlBar) {
        (controlBar as HTMLElement).style.display = "none";
      }

      // 如果启用时间段播放，设置起始时间
      if (useTimeSecond && videos[0]?.startTimeSeconds !== undefined) {
        player.currentTime(videos[0].startTimeSeconds);
      }

      // 默认暂停状态，不自动播放
      player.pause();
      setIsPlaying(false);
    });

    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
      }
    };
  }, [videos, totalDurationSeconds, useTimeSecond]);

  // 处理进度更新
  useEffect(() => {
    if (!playerRef.current) return;

    const player = playerRef.current;

    const updateProgress = () => {
      const currentVideo = videos[currentVideoIndex];
      let currentTimeSeconds;

      if (useTimeSecond) {
        // 当启用时间段播放时，检查是否超过结束时间
        const actualCurrentTime = player.currentTime();
        if (actualCurrentTime >= currentVideo.endTimeSeconds) {
          // 如果超过结束时间，自动切换到下一个视频
          playNextVideo();
          return;
        }

        // 计算相对于片段开始时间的播放时间
        const segmentCurrentTime = actualCurrentTime - currentVideo.startTimeSeconds;
        currentTimeSeconds = accumulatedTime + segmentCurrentTime;
      } else {
        currentTimeSeconds = accumulatedTime + player.currentTime();
      }

      const percent = (currentTimeSeconds / totalDurationSeconds) * 100;
      setProgress(percent);
      setCurrentTime(formatTime(currentTimeSeconds));
    };

    const playNextVideo = () => {
      const currentVideo = videos[currentVideoIndex];
      let segmentDuration;

      if (useTimeSecond) {
        // 使用实际播放的片段时长
        segmentDuration = currentVideo.duration;
      } else {
        segmentDuration = player.currentTime();
      }

      const newAccumulatedTime = accumulatedTime + segmentDuration;
      setAccumulatedTime(newAccumulatedTime);

      const nextIndex = currentVideoIndex + 1;

      if (nextIndex < videos.length) {
        setCurrentVideoIndex(nextIndex);
        const nextVideo = videos[nextIndex];
        player.src(nextVideo.src);

        // 如果启用时间段播放，设置下一个视频的起始时间
        if (useTimeSecond && nextVideo.startTimeSeconds !== undefined) {
          player.one('loadedmetadata', () => {
            player.currentTime(nextVideo.startTimeSeconds);
            player.play();
          });
        } else {
          player.play();
        }
      } else {
        player.pause();
        setIsPlaying(false);
      }
    };

    // 事件监听
    player.on("timeupdate", updateProgress);
    player.on("ended", playNextVideo);

    return () => {
      player.off("timeupdate", updateProgress);
      player.off("ended", playNextVideo);
    };
  }, [accumulatedTime, currentVideoIndex, totalDurationSeconds, videos, useTimeSecond]);

  // 播放/暂停控制
  const handlePlayPause = () => {
    if (!playerRef.current) return;

    if (playerRef.current.paused()) {
      playerRef.current.play();
      setIsPlaying(true);
    } else {
      playerRef.current.pause();
      setIsPlaying(false);
    }
  };

  // 音量控制
  const handleVolumeChange = (newVolume: number) => {
    if (!playerRef.current) return;
    setVolume(newVolume);
    playerRef.current.volume(newVolume / 100);
    setIsMuted(newVolume === 0);
  };

  // 静音切换
  const handleMuteToggle = () => {
    if (!playerRef.current) return;
    if (isMuted) {
      playerRef.current.volume(volume / 100);
      setIsMuted(false);
    } else {
      playerRef.current.volume(0);
      setIsMuted(true);
    }
  };

  // 全屏切换
  const handleFullscreenToggle = () => {
    if (!playerRef.current) return;
    if (isFullscreen) {
      playerRef.current.exitFullscreen();
      setIsFullscreen(false);
    } else {
      playerRef.current.requestFullscreen();
      setIsFullscreen(true);
    }
  };

  // 进度条点击
  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (!playerRef.current) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percent = Math.max(0, Math.min(1, clickX / rect.width));
    const seekTime = percent * totalDurationSeconds;

    // 计算应该跳转到哪个视频
    let timeAccumulated = 0;
    for (let i = 0; i < videos.length; i++) {
      if (seekTime <= timeAccumulated + videos[i].duration) {
        setCurrentVideoIndex(i);
        setAccumulatedTime(timeAccumulated);
        const targetVideo = videos[i];
        playerRef.current.src(targetVideo.src);

        if (useTimeSecond) {
          // 计算在当前片段内的相对时间
          const segmentRelativeTime = seekTime - timeAccumulated;
          const actualTime = targetVideo.startTimeSeconds + segmentRelativeTime;

          // 确保不超过片段的结束时间
          const clampedTime = Math.min(actualTime, targetVideo.endTimeSeconds);

          playerRef.current.one('loadedmetadata', () => {
            playerRef.current.currentTime(clampedTime);
            if (isPlaying) {
              playerRef.current.play();
            }
          });
        } else {
          playerRef.current.currentTime(seekTime - timeAccumulated);
          if (isPlaying) {
            playerRef.current.play();
          }
        }
        break;
      }
      timeAccumulated += videos[i].duration;
    }
  };

  return (
    <div className="relative w-full h-full bg-black font-sans text-white">
      <style
        dangerouslySetInnerHTML={{
          __html: `
          .volume-slider::-webkit-slider-thumb {
            appearance: none;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: white;
            cursor: pointer;
            box-shadow: 0 0 4px rgba(0,0,0,0.3);
          }
          .volume-slider::-moz-range-thumb {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: white;
            cursor: pointer;
            border: none;
            box-shadow: 0 0 4px rgba(0,0,0,0.3);
          }
        `,
        }}
      />
      {/* Video.js 播放器 */}
      <video
        ref={videoRef}
        className="video-js w-full h-full object-contain"
        playsInline
      />

      {/* 底部控制栏 */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
        {/* 进度条区域 */}
        <div className="px-4 pt-4 pb-2">
          <div
            className="relative py-2 cursor-pointer group"
            onClick={handleProgressClick}
          >
            <div className="relative h-1 bg-white/30 rounded-full">
              <div
                className="absolute top-0 left-0 h-full bg-white rounded-full"
                style={{ width: `${progress}%` }}
              />
              <div
                className="absolute top-1/2 w-3 h-3 bg-white rounded-full transition-all duration-200 opacity-0 group-hover:opacity-100 group-hover:w-4 group-hover:h-4"
                style={{
                  left: `${progress}%`,
                  transform: "translateX(-50%) translateY(-50%)",
                  boxShadow: "0 0 8px rgba(255,255,255,0.5)",
                }}
              />
            </div>
          </div>
        </div>

        {/* 控制按钮区域 */}
        <div className="flex items-center justify-between px-4 pb-4">
          {/* 左侧：播放按钮和时间 */}
          <div className="flex items-center space-x-3">
            <button
              onClick={handlePlayPause}
              className="w-8 h-8 flex items-center justify-center text-white hover:bg-white/20 rounded-full transition-all duration-200"
            >
              {isPlaying ? (
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <rect x="6" y="4" width="4" height="16" />
                  <rect x="14" y="4" width="4" height="16" />
                </svg>
              ) : (
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <polygon points="5,3 19,12 5,21" />
                </svg>
              )}
            </button>
            <span className="text-white text-sm font-medium">
              {currentTime} / {totalDuration}
            </span>
          </div>

          {/* 右侧：音量、全屏、更多选项 */}
          <div className="flex items-center space-x-3">
            {/* 音量控制区域 */}
            <div
              className="relative flex items-center"
              onMouseEnter={() => setShowVolumeSlider(true)}
              onMouseLeave={() => setShowVolumeSlider(false)}
            >
              {/* 音量滑块和按钮的组合背景 */}
              <div className={`absolute right-0 top-1/2 transform -translate-y-1/2 bg-black/80 rounded-lg transition-all duration-300 ease-in-out flex items-center ${
                showVolumeSlider ? 'opacity-100' : 'opacity-0 pointer-events-none'
              }`}>
                <div className="p-2">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={isMuted ? 0 : volume}
                    onChange={(e) => handleVolumeChange(Number(e.target.value))}
                    className="w-20 h-1 bg-white/30 rounded-lg appearance-none cursor-pointer volume-slider"
                    style={{
                      background: `linear-gradient(to right, white 0%, white ${
                        isMuted ? 0 : volume
                      }%, rgba(255,255,255,0.3) ${
                        isMuted ? 0 : volume
                      }%, rgba(255,255,255,0.3) 100%)`,
                    }}
                  />
                </div>
                <div className="w-8 h-8 flex items-center justify-center text-white">
                  {isMuted || volume === 0 ? (
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                    >
                      <polygon points="11,5 6,9 2,9 2,15 6,15 11,19" />
                      <line x1="23" y1="9" x2="17" y2="15" />
                      <line x1="17" y1="9" x2="23" y2="15" />
                    </svg>
                  ) : (
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                    >
                      <polygon points="11,5 6,9 2,9 2,15 6,15 11,19" />
                      <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07" />
                    </svg>
                  )}
                </div>
              </div>

              {/* 音量按钮 (始终可见) */}
              <button
                onClick={handleMuteToggle}
                className="w-8 h-8 flex items-center justify-center text-white hover:bg-white/20 rounded-full transition-all duration-200 relative z-10"
              >
                {isMuted || volume === 0 ? (
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <polygon points="11,5 6,9 2,9 2,15 6,15 11,19" />
                    <line x1="23" y1="9" x2="17" y2="15" />
                    <line x1="17" y1="9" x2="23" y2="15" />
                  </svg>
                ) : (
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <polygon points="11,5 6,9 2,9 2,15 6,15 11,19" />
                    <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07" />
                  </svg>
                )}
              </button>
            </div>

            {/* 全屏按钮 */}
            <button
              onClick={handleFullscreenToggle}
              className="w-8 h-8 flex items-center justify-center text-white hover:bg-white/20 rounded-full transition-all duration-200"
            >
              {isFullscreen ? (
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M5 16h3v3h2v-5H5v2zM8 8H5v2h5V5H8v3zm6 8h2v-3h3v-2h-5v5zm2-8V5h-2v5h5V8h-3z" />
                </svg>
              ) : (
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M7 14H5v5h5v-2H7v-3zM5 10h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z" />
                </svg>
              )}
            </button>

            {/* 更多选项 */}
            <button className="w-8 h-8 flex items-center justify-center text-white hover:bg-white/20 rounded-full transition-all duration-200">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <circle cx="12" cy="5" r="2" />
                <circle cx="12" cy="12" r="2" />
                <circle cx="12" cy="19" r="2" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
