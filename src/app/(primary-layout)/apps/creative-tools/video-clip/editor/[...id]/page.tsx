"use client";

import { useParams } from "next/navigation";
import Image from "next/image";
import { getStoryLineDetailInfo, getShortDramaList } from "./services";
import { useEffect, useState, useCallback, useMemo } from "react";
import { ArrowLef<PERSON>, ArrowRight } from "lucide-react";
import { useEditorStore } from "./stores/editor";
import { useTimelineStore } from "./stores/timeline";
import { cn } from "@/lib/utils";
import { Checkbox, Spin, Modal } from "antd";
import { Header } from "./components/header";
import { Preview } from "./components/preview";
import { Timeline } from "./components/timeline";
import { formatSecondsToTime } from "../../utils";

export default function VideoClipEditorPage() {
  // 获取故事线id和切片id
  const params = useParams();
  const storyLineId = params.id[0] || "";
  const videoClipId = params.id[1] || "";

  // 切换Tab
  const [listTab, setListTab] = useState<string>("video-clip");
  // 切片详情
  const [videoClipInfo, setVideoClipInfo] = useState<any>(null);
  // 已选切片
  const {
    selectedClips,
    setSelectedClips,
    activeClip,
    setActiveClip,
    setStoryName,
    videoClips,
    hasUnsavedChanges,
    setHasUnsavedChanges,
    originalSelectedClips,
    setOriginalSelectedClips,
    originalVideoClips,
    setOriginalVideoClips,
  } = useEditorStore();

  // 时间轴控制
  const { scrollToTime } = useTimelineStore();

  // 检测数据是否有变更
  const checkForChanges = useCallback(() => {
    // 检查选中切片是否有变化
    const selectedClipsChanged =
      selectedClips.length !== originalSelectedClips.length ||
      selectedClips.some(
        (clip) => !originalSelectedClips.find((oc) => oc.id === clip.id)
      ) ||
      originalSelectedClips.some(
        (clip) => !selectedClips.find((sc) => sc.id === clip.id)
      );

    // 检查时间轴数据是否有变化（入点出点）
    const videoClipsChanged = videoClips.some((clip) => {
      const originalClip = originalVideoClips.find((vc) => String(vc.id) === String(clip.id));
      return (
        originalClip &&
        (Math.abs(originalClip.startTimeSeconds - clip.inPoint) > 0.1 ||
          Math.abs(originalClip.endTimeSeconds - clip.outPoint) > 0.1)
      );
    });

    const hasChanges = selectedClipsChanged || videoClipsChanged;
    setHasUnsavedChanges(hasChanges);
  }, [
    selectedClips,
    originalSelectedClips,
    videoClips,
    originalVideoClips,
    setHasUnsavedChanges,
  ]);

  // 监听数据变化
  useEffect(() => {
    checkForChanges();
  }, [checkForChanges]);

  // 监听activeClip变化，自动滚动左侧列表到对应切片
  useEffect(() => {
    if (activeClip && listTab === "video-clip") {
      // 延迟执行，确保DOM已经渲染完成
      setTimeout(() => {
        const clipElement = document.querySelector(`[data-clip-id="${activeClip.id}"]`);
        if (clipElement) {
          // 滚动到对应切片位置
          clipElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 100);
    }
  }, [activeClip, listTab]);

  // 页面离开前的提示
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = "您有未保存的更改，确定要离开吗？";
        return "您有未保存的更改，确定要离开吗？";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);

  // 处理激活片段，同时滚动到入点位置
  const handleActivateClip = useCallback(
    (clip: any) => {
      setActiveClip(clip);

      // 延迟滚动，确保时间轴数据已经更新
      setTimeout(() => {
        // 滚动到片段的入点位置
        if (clip && videoClips.length > 0) {
          // 从videoClips中找到对应的时间轴数据
          const timelineClip = videoClips.find(
            (vc: any) => String(vc.id) === String(clip.id)
          );
          if (timelineClip) {
            // 计算入点的全局时间位置
            const inPointTime = timelineClip.startTime + timelineClip.inPoint;
            scrollToTime(inPointTime);
          }
        }
      }, 100); // 100ms延迟确保DOM更新完成
    },
    [setActiveClip, scrollToTime, videoClips]
  );
  // 故事线信息
  const [storyLineInfo, setStoryLineInfo] = useState<any>(null);

  // 添加loading状态
  const [isLoadingVideoClip, setIsLoadingVideoClip] = useState(true);
  const [isLoadingStoryLine, setIsLoadingStoryLine] = useState(true);

  // 获取切片详情
  useEffect(() => {
    const fetchStoryLineDetailInfo = async () => {
      try {
        setIsLoadingVideoClip(true);
        const res = await getStoryLineDetailInfo({ id: videoClipId });
        const result = res.data.result;
        setVideoClipInfo(result);
        // 根据enable字段筛选已启用的切片
        if (result?.storyLineDetailInfoVOList) {
          // 当数据加载完成的时候要给 每个原始数据添加indexNumber
          const newOriginalVideoClips = result.storyLineDetailInfoVOList.map(
            (item: any, index: number) => ({
              ...item,
              indexNumber: index + 1,
            })
          );
          setOriginalVideoClips(newOriginalVideoClips);

          const enabledClips = newOriginalVideoClips.filter(
            (clip: any) => clip.enable === true
          );
          setSelectedClips(enabledClips);
          setOriginalSelectedClips([...enabledClips]); // 保存初始状态
          // 默认激活第一个启用的切片
          if (enabledClips.length > 0) {
            setActiveClip(enabledClips[0]);
          }

          setStoryName(result.storyName);
        }
      } catch (error) {
        console.error("Failed to fetch video clip info:", error);
      } finally {
        setIsLoadingVideoClip(false);
      }
    };
    fetchStoryLineDetailInfo();
  }, [
    videoClipId,
    setSelectedClips,
    setActiveClip,
    setStoryName,
    setOriginalSelectedClips,
    setOriginalVideoClips,
  ]);



  // 获取其他故事线信息
  useEffect(() => {
    const fetchStoryLineInfo = async () => {
      try {
        setIsLoadingStoryLine(true);
        // 使用 videoClipInfo 中的 version
        const version = videoClipInfo?.version;
        const res = await getShortDramaList({ id: storyLineId, version });
        setStoryLineInfo(res.data.result);
      } catch (error) {
        console.error("Failed to fetch story line info:", error);
      } finally {
        setIsLoadingStoryLine(false);
      }
    };

    // 只有当 videoClipInfo 加载完成后才获取故事线信息
    if (videoClipInfo) {
      fetchStoryLineInfo();
    }
  }, [storyLineId, videoClipInfo]);

  // 切换到视频切片列表
  const handleSwitchToVideoClip = useCallback(() => {
    setListTab("video-clip");
    // 切换后滚动到顶部
    setTimeout(() => {
      const listContainer = document.querySelector(
        ".video-clip-list-container"
      );
      if (listContainer) {
        listContainer.scrollTop = 0;
      }
    }, 0);
  }, []);

  // 切换到故事线列表
  const handleSwitchToStoryLine = useCallback(() => {
    setListTab("story-line");
    // 切换后滚动到顶部
    setTimeout(() => {
      const listContainer = document.querySelector(
        ".video-clip-list-container"
      );
      if (listContainer) {
        listContainer.scrollTop = 0;
      }
    }, 0);
  }, []);

  // 列表切换Tab
  const ListTab = useMemo(() => {
    return (
      <div className="flex justify-between items-center text-sm overflow-hidden px-4 h-10">
        <p
          className="font-bold truncate max-w-[120px]"
          title={
            listTab === "story-line" ? "故事线列表" : videoClipInfo?.storyName
          }
        >
          {listTab === "story-line" ? "故事线列表" : videoClipInfo?.storyName}
        </p>

        {listTab === "story-line" && (
          <div
            className="text-blue-500 flex items-center gap-1 cursor-pointer hover:text-blue-400"
            onClick={handleSwitchToVideoClip}
          >
            <ArrowLeft size={16} />
            <p>收起</p>
          </div>
        )}

        {listTab === "video-clip" && (
          <div
            className="text-blue-500 flex items-center gap-1 cursor-pointer hover:text-blue-400"
            onClick={handleSwitchToStoryLine}
          >
            <p>其他故事线</p>
            <ArrowRight size={16} />
          </div>
        )}
      </div>
    );
  }, [
    listTab,
    videoClipInfo?.storyName,
    handleSwitchToVideoClip,
    handleSwitchToStoryLine,
  ]);

  // 处理单个切片选择，保持顺序
  const handleClipSelect = useCallback(
    (clip: any) => {
      const isSelected = selectedClips.some((c: any) => c.id === clip.id);
      if (isSelected) {
        // 取消选择时，从已选列表中移除
        const newClips = selectedClips.filter((c: any) => c.id !== clip.id);
        setSelectedClips(newClips);

        // 如果取消选择的是当前激活的切片，或者没有剩余切片了，清空激活状态
        if (activeClip?.id === clip.id || newClips.length === 0) {
          setActiveClip(newClips.length > 0 ? newClips[0] : null);
        }
      } else {
        // 选择时，按照原始列表的顺序插入到正确位置
        const allClips = originalVideoClips || [];
        const newSelected = [...selectedClips, clip];
        // 根据原始列表的顺序重新排序
        const sortedClips = newSelected.sort((a: any, b: any) => {
          const aIndex = allClips.findIndex((c: any) => c.id === a.id);
          const bIndex = allClips.findIndex((c: any) => c.id === b.id);
          return aIndex - bIndex;
        });
        setSelectedClips(sortedClips);

        // 如果之前没有激活的切片，激活新选择的切片
        if (!activeClip) {
          setActiveClip(clip);
        }
      }
    },
    [
      selectedClips,
      originalVideoClips,
      setSelectedClips,
      activeClip,
      setActiveClip,
    ]
  );

  // 处理全选/取消全选
  const handleSelectAll = useCallback(() => {
    const allClips = originalVideoClips || [];
    if (selectedClips.length === allClips.length) {
      setSelectedClips([]);
      // 取消全选时，清空激活的切片
      setActiveClip(null);
    } else {
      setSelectedClips([...allClips]); // 保持原始顺序
      // 全选时，激活第一个切片
      if (allClips.length > 0) {
        setActiveClip(allClips[0]);
      }
    }
  }, [
    selectedClips.length,
    originalVideoClips,
    setSelectedClips,
    setActiveClip,
  ]);

  // 计算是否全选
  const isAllSelected = useMemo(() => {
    const allClipsLength = originalVideoClips?.length || 0;
    return allClipsLength > 0 && allClipsLength === selectedClips.length;
  }, [originalVideoClips?.length, selectedClips.length]);

  // 切片列表
  const VideoClipList = useMemo(() => {
    if (isLoadingVideoClip) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="flex flex-col items-center gap-4">
            <Spin size="large" />
            <p className="text-gray-500 text-sm">正在加载视频切片...</p>
          </div>
        </div>
      );
    }

    return (
      <div className="flex-1 overflow-y-auto video-clip-list-container">
        <div className="flex items-center gap-2 px-4 pb-4 sticky top-0 bg-white z-50">
          <Checkbox checked={isAllSelected} onChange={handleSelectAll} />
          <span className="text-sm text-gray-700">
            已启用{selectedClips.length}/
            {originalVideoClips?.length}个切片
          </span>
        </div>

        <div className="flex flex-col gap-4">
          {originalVideoClips?.map(
            (item: any, index: number) => {
              const isSelected = selectedClips.some((c) => c.id === item.id);
              return (
                <div
                  key={item.id}
                  data-clip-id={item.id}
                  className={cn(
                    "text-sm flex flex-col cursor-pointer rounded-lg mx-4 relative",
                    activeClip?.id === item.id
                      ? "border border-blue-500"
                      : "border border-transparent hover:bg-gray-50"
                  )}
                  onClick={() => handleActivateClip(item)}
                >
                  <div className="absolute left-3 top-3 z-10">
                    <Checkbox
                      checked={isSelected}
                      onChange={() => handleClipSelect(item)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>

                  <div className="absolute right-3 top-3 z-10">
                    <p className="text-xs text-white bg-black/50 px-2 py-1 rounded-md">
                      {(item.endTimeSeconds - item.startTimeSeconds).toFixed(1)}
                      s
                    </p>
                  </div>

                  <div className="relative w-full rounded-md overflow-hidden bg-gray-100 aspect-square">
                    {item.videoClipPreviewUrl ? (
                      <Image
                        src={item.videoClipPreviewUrl}
                        alt={item.script || "视频预览"}
                        className="object-cover"
                        fill
                        unoptimized
                      />
                    ) : (
                      <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                        暂无预览图
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col gap-2 p-2">
                    <p className="font-bold line-clamp-2">{item.script}</p>
                    <div className="text-gray-500 flex items-center gap-2 justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <p>第{item.episode}集</p>
                        <p>
                          {formatSecondsToTime(item.startTimeSeconds)} -{" "}
                          {formatSecondsToTime(item.endTimeSeconds)}
                        </p>
                      </div>
                      <p>片段{index + 1}</p>
                    </div>
                  </div>
                </div>
              );
            }
          )}
        </div>
      </div>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    isLoadingVideoClip,
    videoClipInfo?.storyLineDetailInfoVOList,
    selectedClips,
    isAllSelected,
    handleSelectAll,
    handleClipSelect,
    activeClip,
    setActiveClip,
  ]);

  // 其他故事线列表
  const StoryLineList = useMemo(() => {
    if (isLoadingStoryLine) {
      return (
        <div className="flex-1 flex items-center justify-center">
          <div className="flex flex-col items-center gap-4">
            <Spin size="large" />
            <p className="text-gray-500 text-sm">正在加载故事线列表...</p>
          </div>
        </div>
      );
    }

    return (
      <div className="flex-1 overflow-y-auto flex flex-col gap-4 px-4 video-clip-list-container">
        {storyLineInfo?.storyLineInfoVOList?.map((item: any) => (
          <div
            key={item.id}
            className="flex gap-3 cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors"
          >
            {/* 预览图片容器 */}
            <div className="flex-shrink-0">
              <div className="relative overflow-hidden bg-gray-100 w-[80px] h-[120px]">
                {item.previewUrl ? (
                  <Image
                    src={item.previewUrl}
                    alt={item.storyName || "故事线预览"}
                    className="object-cover"
                    fill
                    unoptimized
                  />
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center text-gray-400 text-xs">
                    暂无预览
                  </div>
                )}
              </div>
            </div>

            {/* 内容信息 */}
            <div className="flex-1 flex flex-col gap-2 min-w-0">
              <h4 className="text-sm font-semibold text-gray-900 line-clamp-2 leading-tight">
                {item.storyName}
              </h4>

              <p className="text-xs text-gray-500 line-clamp-2 leading-relaxed">
                {item.reason || "暂无描述"}
              </p>

              <p className="text-xs text-gray-400">
                时长: {formatSecondsToTime(item.duration)}
              </p>

              <p
                className="text-xs my-1 text-blue-500 hover:text-blue-600 transition-colors"
                onClick={() => {
                  window.open(
                    `/dam/apps/creative-tools/video-clip/editor/${storyLineId}/${item.id}`,
                    "_blank"
                  );
                }}
              >
                查看切片({item?.storyLineDetailCount || 3})
              </p>
            </div>
          </div>
        ))}
      </div>
    );
  }, [isLoadingStoryLine, storyLineInfo?.storyLineInfoVOList, storyLineId]);

  const currentVersion = storyLineInfo?.storyLineInfoVOList?.[0]?.version;

  return (
    <div className="h-screen flex flex-col">
      <Header
        id={videoClipId}
        taskId={storyLineId}
        allClips={videoClipInfo?.storyLineDetailInfoVOList || []}
        version={currentVersion+1}
        taskName={storyLineInfo?.taskName}
      />
      <div className="flex overflow-y-scroll flex-1">
        <div className="w-[280px] flex flex-col flex-shrink-0">
          {ListTab}
          {listTab === "video-clip" ? VideoClipList : StoryLineList}
        </div>
        <div className="flex-1 flex justify-between">
          <Timeline />
          <Preview />
        </div>
      </div>
    </div>
  );
}
