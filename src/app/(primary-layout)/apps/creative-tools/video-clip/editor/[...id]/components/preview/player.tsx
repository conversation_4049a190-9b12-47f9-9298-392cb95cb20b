import { useRef, useEffect, useState, useMemo } from "react";
import { useEditorStore } from "../../stores/editor";
import { usePreviewStore } from "../../stores/preview";

interface PlayerProps {
  currentTime: number;
  isPlaying: boolean;
  onPlay: () => void;
  onTimeUpdate: (time: number) => void;
  onVideoDurationChange: (duration: number) => void;
  onClipActivate?: (clipId: string) => void;
}

export function Player({
  currentTime,
  isPlaying,
  onPlay,
  onTimeUpdate,
  onVideoDurationChange,
  onClipActivate,
}: PlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [loadingError, setLoadingError] = useState<string | null>(null);

  const { videoClips, activeClip } = useEditorStore();
  const { playMode } = usePreviewStore();

  /**
   * 根据播放模式和当前时间确定要播放的视频
   */
  const currentVideo = useMemo(() => {
    if (!videoClips || videoClips.length === 0) return null;

    switch (playMode) {
      case "single":
        // 播放当前激活的切片
        return activeClip
          ? videoClips.find((clip) => clip.id === activeClip.id)
          : null;

      case "all":
        // 预览素材成片：根据当前时间找到对应的切片
        return videoClips.find(
          (clip) => currentTime >= clip.startTime && currentTime < clip.endTime
        );

      default:
        // 默认模式：显示第一个视频的第一帧
        return videoClips[0] || null;
    }
  }, [videoClips, activeClip, playMode, currentTime]);

  /**
   * 计算视频内的相对时间
   */
  const videoTime = useMemo(() => {
    if (!currentVideo) return 0;

    switch (playMode) {
      case "single":
        // 单片播放：使用入点到出点的时间
        return currentVideo.inPoint;

      case "all":
        // 成片播放：计算在当前切片内的相对时间
        const relativeTime = currentTime - currentVideo.startTime;
        return currentVideo.inPoint + relativeTime;

      default:
        // 默认模式：显示入点
        return currentVideo.inPoint;
    }
  }, [currentVideo, currentTime, playMode]);

  /**
   * 处理视频加载
   */
  useEffect(() => {
    const video = videoRef.current;
    if (!video || !currentVideo) return;

    // 如果视频URL没有变化，不需要重新加载
    if (video.src === currentVideo.videoUrl) {
      // 只更新时间
      if (Math.abs(video.currentTime - videoTime) > 0.1) {
        video.currentTime = videoTime;
      }
      return;
    }

    console.log("加载视频:", currentVideo.videoUrl);
    setIsVideoLoaded(false);
    setLoadingError(null);

    video.pause();
    video.src = currentVideo.videoUrl;
    video.load();

    const handleLoadedMetadata = () => {
      console.log("视频元数据加载完成");
      setIsVideoLoaded(true);
      video.currentTime = videoTime;
      onVideoDurationChange(video.duration);
      video.removeEventListener("loadedmetadata", handleLoadedMetadata);
    };

    const handleError = () => {
      console.error("视频加载失败:", video.error);
      setLoadingError("视频加载失败");
      setIsVideoLoaded(false);
    };

    video.addEventListener("loadedmetadata", handleLoadedMetadata);
    video.addEventListener("error", handleError);

    return () => {
      video.removeEventListener("loadedmetadata", handleLoadedMetadata);
      video.removeEventListener("error", handleError);
    };
  }, [currentVideo, videoTime, onVideoDurationChange]);

  /**
   * 处理播放控制
   */
  useEffect(() => {
    const video = videoRef.current;
    if (!video || !isVideoLoaded) return;

    if (isPlaying && playMode !== "default") {
      video.play().catch((e) => console.error("Play failed", e));
    } else {
      video.pause();
    }
  }, [isPlaying, isVideoLoaded, playMode]);

  /**
   * 处理时间更新
   */
  useEffect(() => {
    const video = videoRef.current;
    if (!video || !isVideoLoaded || !currentVideo) return;

    const handleTimeUpdate = () => {
      if (playMode === "all") {
        // 成片播放模式：将视频时间转换为全局时间
        const relativeTime = video.currentTime - currentVideo.inPoint;
        const globalTime = currentVideo.startTime + relativeTime;

        // 检查是否超出当前切片的出点
        if (video.currentTime >= currentVideo.outPoint) {
          // 跳到下一个切片或停止播放
          const nextClip = videoClips.find(
            (clip) => clip.startTime >= currentVideo.endTime
          );
          if (nextClip) {
            onTimeUpdate(nextClip.startTime);
            onClipActivate?.(nextClip.id);
          } else {
            // 没有下一个切片，停止播放
            onPlay();
          }
          return;
        }

        onTimeUpdate(globalTime);
      } else if (playMode === "single") {
        // 单片播放模式：检查是否超出出点
        if (video.currentTime >= currentVideo.outPoint) {
          video.pause();
          onPlay(); // 停止播放
          return;
        }

        // 更新时间轴到对应的全局时间
        const relativeTime = video.currentTime - currentVideo.inPoint;
        const globalTime = currentVideo.startTime + relativeTime;
        onTimeUpdate(globalTime);
      }
    };

    video.addEventListener("timeupdate", handleTimeUpdate);
    return () => video.removeEventListener("timeupdate", handleTimeUpdate);
  }, [
    isVideoLoaded,
    currentVideo,
    playMode,
    onTimeUpdate,
    onPlay,
    videoClips,
    onClipActivate,
  ]);

  return (
    <div
      className="relative flex items-center justify-center w-full bg-black"
      style={{ aspectRatio: "9/16", minHeight: "400px" }}
    >
      {/* 视频元素 */}
      <video
        ref={videoRef}
        className="w-full h-full object-contain"
        crossOrigin="anonymous"
        preload="metadata"
        playsInline
        muted
      />

      {/* 加载状态 */}
      {!isVideoLoaded && !loadingError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 text-white">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p>加载中...</p>
          </div>
        </div>
      )}

      {/* 错误状态 */}
      {loadingError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 text-white">
          <div className="text-center">
            <p className="text-red-400 mb-2">⚠️ {loadingError}</p>
            <p className="text-sm text-gray-400">请检查视频文件是否可用</p>
          </div>
        </div>
      )}

      {/* 无视频状态 */}
      {!currentVideo && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 text-white">
          <div className="text-center">
            <p className="text-gray-400">暂无视频</p>
          </div>
        </div>
      )}
    </div>
  );
}
