import { useRef, useEffect, useCallback, useState } from "react";
import { useEditorStore } from "../../stores/editor";
import { VideoClipData } from "../../types";
import { ComposedTimelineItem } from "./index";

interface PlayerProps {
  currentTime: number;
  isPlaying: boolean;
  playbackSpeed: number;
  onPlay: () => void;
  onTimeUpdate: (time: number) => void;
  onVideoDurationChange: (duration: number) => void;
  videoClips: VideoClipData[];
  composedTimeline: ComposedTimelineItem[];
  playMode: "composed" | "single";
  onClipActivate?: (clipId: string) => void; // 新增：片段激活回调
}

// 文本换行处理函数
const wrapText = (
  ctx: CanvasRenderingContext2D,
  text: string,
  maxWidth: number
): string[] => {
  const lines: string[] = [];
  let currentLine = "";

  // 检测是否包含中文字符
  const hasChinese = /[\u4e00-\u9fa5]/.test(text);

  if (hasChinese) {
    // 中文文本处理：按字符逐个检查
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      const testLine = currentLine + char;
      const testWidth = ctx.measureText(testLine).width;

      if (testWidth <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine) {
          lines.push(currentLine);
          currentLine = char;
        } else {
          // 单个字符就超过最大宽度，强制添加
          lines.push(char);
          currentLine = "";
        }
      }
    }
  } else {
    // 英文文本处理：按单词分割
    const words = text.split(" ");
    
    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const testWidth = ctx.measureText(testLine).width;

      if (testWidth <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          // 单词本身就超过最大宽度，强制换行
          lines.push(word);
          currentLine = "";
        }
      }
    }
  }

  if (currentLine) {
    lines.push(currentLine);
  }

  return lines;
};

// 绘制圆角矩形
const drawRoundedRect = (
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  width: number,
  height: number,
  radius: number
) => {
  ctx.beginPath();
  ctx.moveTo(x + radius, y);
  ctx.lineTo(x + width - radius, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
  ctx.lineTo(x + width, y + height - radius);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
  ctx.lineTo(x + radius, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
  ctx.lineTo(x, y + radius);
  ctx.quadraticCurveTo(x, y, x + radius, y);
  ctx.closePath();
};

export function Player({
  currentTime,
  isPlaying,
  playbackSpeed,
  onPlay,
  onTimeUpdate,
  onVideoDurationChange,
  videoClips,
  composedTimeline,
  playMode,
  onClipActivate,
}: PlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [canvasDimensions, setCanvasDimensions] = useState({
    width: 360,
    height: 640,
  });
  const [videoAspectRatio, setVideoAspectRatio] = useState(9 / 16); // 默认竖屏比例
  const [isFullscreen, setIsFullscreen] = useState(false);

  const { showSubtitles, subtitleClips } = useEditorStore();

  // 检测全屏状态
  const checkFullscreenState = useCallback(() => {
    const fullscreenElement =
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).mozFullScreenElement ||
      (document as any).msFullscreenElement;
    return !!fullscreenElement;
  }, []);

  // 计算响应式Canvas尺寸
  const calculateCanvasDimensions = useCallback(() => {
    const currentIsFullscreen = checkFullscreenState();

    if (currentIsFullscreen) {
      // 全屏时：以100vh为基准高度，根据视频比例计算宽度
      const targetHeight = window.innerHeight;
      const targetWidth = targetHeight * videoAspectRatio;

      // 如果计算出的宽度超过屏幕宽度，则以屏幕宽度为准重新计算高度
      if (targetWidth > window.innerWidth) {
        return {
          width: window.innerWidth,
          height: Math.floor(window.innerWidth / videoAspectRatio),
        };
      }

      return {
        width: Math.floor(targetWidth),
        height: targetHeight,
      };
    } else {
      // 非全屏时：使用60vh
      const targetHeight = window.innerHeight * 0.6;
      const targetWidth = targetHeight * videoAspectRatio;

      // 确保宽度不超过窗口宽度的90%
      const maxWidth = window.innerWidth * 0.9;

      let finalWidth = targetWidth;
      let finalHeight = targetHeight;

      if (targetWidth > maxWidth) {
        finalWidth = maxWidth;
        finalHeight = maxWidth / videoAspectRatio;
      }

      return {
        width: Math.floor(finalWidth),
        height: Math.floor(finalHeight),
      };
    }
  }, [videoAspectRatio, checkFullscreenState]);

  // 全屏状态变化监听
  useEffect(() => {
    const handleFullscreenChange = () => {
      const currentFullscreenState = checkFullscreenState();
      setIsFullscreen(currentFullscreenState);

      // 全屏状态变化时重新计算尺寸
      const newDimensions = calculateCanvasDimensions();
      setCanvasDimensions(newDimensions);
    };

    // 监听各种全屏事件
    document.addEventListener("fullscreenchange", handleFullscreenChange);
    document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
    document.addEventListener("mozfullscreenchange", handleFullscreenChange);
    document.addEventListener("MSFullscreenChange", handleFullscreenChange);

    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
      document.removeEventListener(
        "webkitfullscreenchange",
        handleFullscreenChange
      );
      document.removeEventListener(
        "mozfullscreenchange",
        handleFullscreenChange
      );
      document.removeEventListener(
        "MSFullscreenChange",
        handleFullscreenChange
      );
    };
  }, [calculateCanvasDimensions, checkFullscreenState]);

  // 窗口大小变化监听
  useEffect(() => {
    const handleResize = () => {
      const newDimensions = calculateCanvasDimensions();
      setCanvasDimensions(newDimensions);
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [calculateCanvasDimensions]);

  // 获取当前应该播放的视频和时间
  const getCurrentComposedItem = () => {
    if (playMode === "single" || composedTimeline.length === 0) {
      return composedTimeline[0] || null;
    }
    return composedTimeline.find(
      (item) => currentTime >= item.globalStart && currentTime < item.globalEnd
    );
  };

  const currentComposedItem = getCurrentComposedItem();
  const currentVideoUrl = currentComposedItem?.videoUrl || "";
  const currentInPoint = currentComposedItem?.inPoint || 0;
  const currentOutPoint = currentComposedItem?.outPoint || 0;
  const currentGlobalStart = currentComposedItem?.globalStart || 0;

  // 添加前一个片段的引用，用于检测片段切换
  const prevComposedItemRef = useRef<ComposedTimelineItem | null>(null);

  // 获取当前应该使用的视频元素
  const getCurrentVideoElement = useCallback(() => {
    return videoRef.current;
  }, []);

  // 初始化Canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const { width, height } = canvasDimensions;
    canvas.width = width;
    canvas.height = height;
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;

    const ctx = canvas.getContext("2d");
    if (ctx) {
      ctx.fillStyle = "#000000";
      ctx.fillRect(0, 0, width, height);
    }
  }, [canvasDimensions, currentVideoUrl]);

  // Canvas绘制函数
  const drawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const video = getCurrentVideoElement();
    if (!canvas || !video || !isVideoLoaded) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 设置黑色背景
    ctx.fillStyle = "#000000";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 绘制视频帧
    const videoWidth = video.videoWidth;
    const videoHeight = video.videoHeight;

    if (videoWidth > 0 && videoHeight > 0) {
      const canvasWidth = canvas.width;
      const canvasHeight = canvas.height;

      // 计算视频在Canvas中的显示尺寸和位置（保持宽高比，居中显示）
      const videoAspect = videoWidth / videoHeight;
      const canvasAspect = canvasWidth / canvasHeight;

      let drawWidth, drawHeight, drawX, drawY;

      if (videoAspect > canvasAspect) {
        // 视频更宽，以宽度为准
        drawWidth = canvasWidth;
        drawHeight = canvasWidth / videoAspect;
        drawX = 0;
        drawY = (canvasHeight - drawHeight) / 2;
      } else {
        // 视频更高，以高度为准
        drawHeight = canvasHeight;
        drawWidth = canvasHeight * videoAspect;
        drawX = (canvasWidth - drawWidth) / 2;
        drawY = 0;
      }

      // 绘制视频帧
      try {
        ctx.drawImage(video, drawX, drawY, drawWidth, drawHeight);
      } catch (error) {
        console.warn("Canvas drawImage error:", error);
      }
    }

    // 绘制字幕
    if (showSubtitles && subtitleClips && subtitleClips.length > 0) {
      const currentSubtitle = subtitleClips.find(
        (subtitle) =>
          currentTime >= subtitle.startTime && currentTime <= subtitle.endTime
      );

      if (currentSubtitle && currentSubtitle.translationText) {
        const centerX = canvas.width / 2;
        const bottomMargin = 40;
        const lineHeight = 24;
        const maxWidth = canvas.width - 40; // 左右留20px边距
        const padding = 12; // 背景内边距
        const borderRadius = 8; // 圆角半径

        // 设置字幕样式
        ctx.font = "16px Arial, sans-serif";
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";

        // 只显示翻译文本
        const lines = wrapText(ctx, currentSubtitle.translationText, maxWidth);
        const totalHeight = lines.length * lineHeight;
        const startY = canvas.height - bottomMargin - totalHeight + lineHeight / 2;

        // 计算背景矩形的尺寸
        let maxLineWidth = 0;
        lines.forEach((line: string) => {
          const lineWidth = ctx.measureText(line).width;
          if (lineWidth > maxLineWidth) {
            maxLineWidth = lineWidth;
          }
        });

        // 背景矩形参数
        const bgWidth = Math.min(maxLineWidth + padding * 2, canvas.width - 20);
        const bgHeight = totalHeight + padding * 2;
        const bgX = centerX - bgWidth / 2;
        const bgY = startY - lineHeight / 2 - padding;

        // 绘制半透明黑色背景
        ctx.fillStyle = "rgba(0, 0, 0, 0.75)";
        drawRoundedRect(ctx, bgX, bgY, bgWidth, bgHeight, borderRadius);
        ctx.fill();

        // 绘制翻译字幕文字
        ctx.fillStyle = "#ffffff";
        ctx.strokeStyle = "#000000";
        ctx.lineWidth = 2;

        lines.forEach((line: string, index: number) => {
          const y = startY + index * lineHeight;
          ctx.strokeText(line, centerX, y);
          ctx.fillText(line, centerX, y);
        });
      }
    }
  }, [currentTime, subtitleClips, showSubtitles, isVideoLoaded, getCurrentVideoElement]);

  // 动画循环
  useEffect(() => {
    let animationId: number;

    const animate = () => {
      drawCanvas();
      animationId = requestAnimationFrame(animate);
    };

    if (isVideoLoaded) {
      animate();
    }

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [drawCanvas, isVideoLoaded]);

  // 处理视频加载
  const handleLoadedData = useCallback(() => {
    const video = getCurrentVideoElement();
    const canvas = canvasRef.current;
    if (!video || !canvas) return;

    const videoWidth = video.videoWidth;
    const videoHeight = video.videoHeight;

    setIsVideoLoaded(true);
    setLoadingError(null);

    onVideoDurationChange(video.duration);

    // 计算并保存视频宽高比
    const aspectRatio = videoWidth / videoHeight;
    setVideoAspectRatio(aspectRatio);

    // 重新计算响应式尺寸
    const newDimensions = calculateCanvasDimensions();
    setCanvasDimensions(newDimensions);

    if (isPlaying) {
      video.play().catch((e: any) => console.error("Auto-play failed", e));
    }
  }, [onVideoDurationChange, isPlaying, calculateCanvasDimensions, getCurrentVideoElement]);

  // 当视频宽高比改变时重新计算尺寸
  useEffect(() => {
    if (videoAspectRatio && isVideoLoaded) {
      const newDimensions = calculateCanvasDimensions();
      setCanvasDimensions(newDimensions);
    }
  }, [videoAspectRatio, calculateCanvasDimensions, isVideoLoaded]);

  // 视频播放控制
  useEffect(() => {
    const video = getCurrentVideoElement();
    if (!video || !isVideoLoaded) return;

    if (isPlaying) {
      video.play().catch((e) => console.error("Play failed", e));
    } else {
      video.pause();
    }
  }, [isPlaying, isVideoLoaded, getCurrentVideoElement]);

  // 检测片段切换
  useEffect(() => {
    const prevItem = prevComposedItemRef.current;
    const hasClipChanged = prevItem?.clipId !== currentComposedItem?.clipId;

    if (hasClipChanged && currentComposedItem) {
      console.log('片段切换:', prevItem?.clipId, '->', currentComposedItem.clipId);

      console.log('使用主视频元素');
      // 使用主视频元素
      const video = videoRef.current;
      if (video && video.src !== currentVideoUrl) {
        setIsVideoLoaded(false);
        video.pause();
        video.src = currentVideoUrl;
        video.load();
      }
    }

    prevComposedItemRef.current = currentComposedItem || null;
  }, [currentComposedItem, currentVideoUrl, currentTime]);

  // 视频时间同步
  useEffect(() => {
    const video = getCurrentVideoElement();
    if (!video || !isVideoLoaded || !currentComposedItem) return;

    const relativeTime = currentTime - currentGlobalStart;
    const videoPlayTime = currentInPoint + relativeTime;
    const timeDiff = Math.abs(video.currentTime - videoPlayTime);

    // 增加时间同步的容差，避免频繁跳转
    if (timeDiff > 0.2) {
      console.log('时间同步:', video.currentTime, '->', videoPlayTime);
      video.currentTime = videoPlayTime;
    }
  }, [currentTime, currentComposedItem, isVideoLoaded, currentGlobalStart, currentInPoint, getCurrentVideoElement]);

  // 处理视频时间更新
  const handleTimeUpdate = useCallback((event?: any) => {
    const video = getCurrentVideoElement();
    if (!video || !isPlaying || !currentComposedItem) return;

    // 如果事件来自特定的视频元素，确保它是当前活跃的视频
    if (event && event.target && event.target !== video) {
      return; // 忽略非当前活跃视频的时间更新事件
    }

    const relativeTime = video.currentTime - currentInPoint;

    // 检查是否超过当前片段的出点
    if (video.currentTime >= currentOutPoint) {
      if (playMode === "composed") {
        // 自动切换到下一个片段
        const nextIndex =
          composedTimeline.findIndex(
            (item) => item.clipId === currentComposedItem.clipId
          ) + 1;
        if (nextIndex < composedTimeline.length) {
          const nextItem = composedTimeline[nextIndex];
          onTimeUpdate(nextItem.globalStart);
          onClipActivate?.(nextItem.clipId); // 激活下一个片段
        } else {
          // 播放结束，停止播放
          console.log('播放完成，自动暂停');
          onTimeUpdate(currentComposedItem.globalEnd);

          // 暂停视频元素
          video.pause();

          // 触发暂停事件
          setTimeout(() => {
            // 这里可以触发播放器的暂停状态
            // onPlay(); // 如果需要切换播放状态
          }, 100);
        }
        return;
      } else {
        // 单片播放模式，停留在出点
        onTimeUpdate(
          currentComposedItem.globalStart + (currentOutPoint - currentInPoint)
        );
        setTimeout(() => {
          if (video) video.pause();
        }, 100);
        return;
      }
    }

    // 更新全局时间
    const globalTime = currentGlobalStart + relativeTime;
    onTimeUpdate(globalTime);
  }, [getCurrentVideoElement, isPlaying, currentComposedItem, currentInPoint, currentOutPoint, currentGlobalStart, onTimeUpdate, playMode, composedTimeline, onClipActivate]);

  // 视频加载处理
  useEffect(() => {
    const video = videoRef.current;
    if (!video || !currentVideoUrl) return;

    // 如果视频URL没有变化，不需要重新加载
    if (video.src === currentVideoUrl) return;

    console.log('加载视频:', currentVideoUrl);
    setIsVideoLoaded(false);
    setLoadingError(null);

    video.pause();
    video.src = currentVideoUrl;
    video.load();

    // 视频加载完成后，设置到片段起点
    const handleLoadedMetadata = () => {
      console.log('视频元数据加载完成');
      setIsVideoLoaded(true);
      if (currentComposedItem) {
        video.currentTime = currentComposedItem.inPoint;
        console.log('设置视频时间到起点:', currentComposedItem.inPoint);
      }
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
    };

    const handleError = () => {
      console.error('视频加载失败:', video.error);
      setLoadingError('视频加载失败');
      setIsVideoLoaded(false);
    };

    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('error', handleError);

    // 清理函数
    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('error', handleError);
    };
  }, [currentVideoUrl, currentComposedItem]);

  // 视频加载完成后的精确时间同步
  useEffect(() => {
    const video = getCurrentVideoElement();
    if (!video || !isVideoLoaded || !currentComposedItem) return;

    const relativeTime = currentTime - currentGlobalStart;
    const videoPlayTime = currentInPoint + relativeTime;

    // 确保时间在有效范围内
    if (videoPlayTime >= currentInPoint && videoPlayTime <= currentOutPoint) {
      const timeDiff = Math.abs(video.currentTime - videoPlayTime);
      if (timeDiff > 0.1) {
        console.log('精确时间同步:', video.currentTime, '->', videoPlayTime);
        video.currentTime = videoPlayTime;
      }
    }
  }, [isVideoLoaded, currentTime, currentComposedItem, currentGlobalStart, currentInPoint, currentOutPoint, getCurrentVideoElement]);

  const handleError = () => {
    const video = getCurrentVideoElement();
    const errorMessage = video?.error?.message || "视频加载失败";
    setLoadingError(errorMessage);
    setIsVideoLoaded(false);
  };

  // 播放速度控制
  useEffect(() => {
    const video = getCurrentVideoElement();
    if (video) {
      video.playbackRate = playbackSpeed;
    }
  }, [playbackSpeed, getCurrentVideoElement]);



  // 动态计算容器的最小高度和样式
  const containerMinHeight = isFullscreen ? "100vh" : "60vh";
  const containerMaxHeight = isFullscreen ? "100vh" : "80vh";

  return (
    <div
      className="relative flex items-center justify-center w-full"
      style={{
        minHeight: containerMinHeight,
        height: isFullscreen ? "100vh" : "auto",
      }}
    >
      {/* 隐藏的视频元素 */}
      <video
        ref={videoRef}
        style={{ display: "none" }}
        onLoadedData={handleLoadedData}
        onError={handleError}
        onTimeUpdate={handleTimeUpdate}
        crossOrigin="anonymous"
        preload="auto"
        playsInline
      />

      <div
        className="relative bg-black flex items-center justify-center overflow-hidden"
        style={{
          width: `${canvasDimensions.width}px`,
          height: `${canvasDimensions.height}px`,
          maxWidth: isFullscreen ? "100vw" : "90vw",
          maxHeight: containerMaxHeight,
        }}
      >
        {/* Canvas渲染层 */}
        <canvas
          ref={canvasRef}
          className="w-full h-full"
          style={{
            display: "block",
            backgroundColor: "#000000",
            width: `${canvasDimensions.width}px`,
            height: `${canvasDimensions.height}px`,
          }}
        />

        {/* 加载状态 */}
        {!isVideoLoaded && !loadingError && currentComposedItem && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
              <div>视频加载中...</div>
            </div>
          </div>
        )}

        {/* 错误状态 */}
        {loadingError && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div className="text-red-500 text-center p-4">
              <div className="mb-2">视频加载失败</div>
              <div className="text-sm">{loadingError}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
