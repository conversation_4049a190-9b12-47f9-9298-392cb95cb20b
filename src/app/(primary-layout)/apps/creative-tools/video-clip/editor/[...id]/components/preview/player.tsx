import { useRef, useEffect, useState, useMemo } from "react";
import { useEditorStore } from "../../stores/editor";
import { usePreviewStore } from "../../stores/preview";

interface PlayerProps {
  currentTime: number;
  isPlaying: boolean;
  onPlay: () => void;
  onTimeUpdate: (time: number) => void;
  onVideoDurationChange: (duration: number) => void;
  onClipActivate?: (clipId: string) => void;
}

export function Player({
  currentTime,
  isPlaying,
  onPlay,
  onTimeUpdate,
  onVideoDurationChange,
  onClipActivate,
}: PlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [loadingError, setLoadingError] = useState<string | null>(null);

  const { videoClips, activeClip } = useEditorStore();
  const { playMode } = usePreviewStore();

  /**
   * 根据播放模式和当前时间确定要播放的视频
   */
  const currentVideo = useMemo(() => {
    console.log("Player - 计算当前视频:", {
      videoClips: videoClips?.length,
      activeClip: activeClip?.id,
      playMode,
      currentTime,
    });

    if (!videoClips || videoClips.length === 0) {
      console.log("Player - 没有视频切片");
      return null;
    }

    let selectedVideo = null;
    switch (playMode) {
      case "single":
        // 播放当前激活的切片
        selectedVideo = activeClip
          ? videoClips.find((clip) => clip.id === activeClip.id)
          : null;
        console.log("Player - 单片模式，选中视频:", selectedVideo?.id);
        break;

      case "all":
        // 预览素材成片：根据当前时间找到对应的切片
        selectedVideo = videoClips.find(
          (clip) => currentTime >= clip.startTime && currentTime < clip.endTime
        );
        console.log("Player - 成片模式，选中视频:", selectedVideo?.id);
        break;

      default:
        // 默认模式：显示第一个视频的第一帧
        selectedVideo = videoClips[0] || null;
        console.log("Player - 默认模式，选中视频:", selectedVideo?.id);
        break;
    }

    if (selectedVideo) {
      console.log("Player - 当前视频详情:", {
        id: selectedVideo.id,
        videoUrl: selectedVideo.videoUrl,
        inPoint: selectedVideo.inPoint,
        outPoint: selectedVideo.outPoint,
      });
    }

    return selectedVideo;
  }, [videoClips, activeClip, playMode, currentTime]);

  /**
   * 计算视频内的相对时间
   */
  const videoTime = useMemo(() => {
    if (!currentVideo) return 0;

    switch (playMode) {
      case "single":
        // 单片播放：使用入点到出点的时间
        return currentVideo.inPoint;

      case "all":
        // 成片播放：计算在当前切片内的相对时间
        const relativeTime = currentTime - currentVideo.startTime;
        return currentVideo.inPoint + relativeTime;

      default:
        // 默认模式：显示入点
        return currentVideo.inPoint;
    }
  }, [currentVideo, currentTime, playMode]);

  /**
   * 处理视频加载
   */
  useEffect(() => {
    const video = videoRef.current;
    console.log("Player - 视频加载 useEffect:", {
      hasVideo: !!video,
      hasCurrentVideo: !!currentVideo,
      currentVideoUrl: currentVideo?.videoUrl,
      currentVideoSrc: video?.src,
    });

    if (!video || !currentVideo) {
      console.log("Player - 跳过加载：缺少视频元素或当前视频");
      return;
    }

    // 如果视频URL没有变化，不需要重新加载
    if (video.src === currentVideo.videoUrl) {
      console.log("Player - 视频URL未变化，只更新时间");
      // 只更新时间
      if (Math.abs(video.currentTime - videoTime) > 0.1) {
        video.currentTime = videoTime;
      }
      return;
    }

    console.log("Player - 开始加载新视频:", {
      url: currentVideo.videoUrl,
      videoTime,
      inPoint: currentVideo.inPoint,
      outPoint: currentVideo.outPoint,
    });
    setIsVideoLoaded(false);
    setLoadingError(null);

    video.pause();
    video.src = currentVideo.videoUrl;
    video.load();

    const handleLoadedMetadata = () => {
      console.log("Player - 视频元数据加载完成:", {
        duration: video.duration,
        videoWidth: video.videoWidth,
        videoHeight: video.videoHeight,
        readyState: video.readyState,
      });
      setIsVideoLoaded(true);
      video.currentTime = videoTime;
      onVideoDurationChange(video.duration);
      video.removeEventListener("loadedmetadata", handleLoadedMetadata);
    };

    const handleLoadedData = () => {
      console.log("Player - 视频数据加载完成");
      setIsVideoLoaded(true);
      video.removeEventListener("loadeddata", handleLoadedData);
    };

    const handleCanPlay = () => {
      console.log("Player - 视频可以播放");
      setIsVideoLoaded(true);
      video.removeEventListener("canplay", handleCanPlay);
    };

    const handleError = () => {
      console.error("Player - 视频加载失败:", {
        error: video.error,
        networkState: video.networkState,
        readyState: video.readyState,
        src: video.src,
      });
      setLoadingError(`视频加载失败: ${video.error?.message || "未知错误"}`);
      setIsVideoLoaded(false);
    };

    // 添加多个事件监听器以确保能够正确检测到视频加载完成
    video.addEventListener("loadedmetadata", handleLoadedMetadata);
    video.addEventListener("loadeddata", handleLoadedData);
    video.addEventListener("canplay", handleCanPlay);
    video.addEventListener("error", handleError);

    return () => {
      video.removeEventListener("loadedmetadata", handleLoadedMetadata);
      video.removeEventListener("loadeddata", handleLoadedData);
      video.removeEventListener("canplay", handleCanPlay);
      video.removeEventListener("error", handleError);
    };
  }, [currentVideo, videoTime, onVideoDurationChange]);

  /**
   * 处理播放控制
   */
  useEffect(() => {
    const video = videoRef.current;
    if (!video || !isVideoLoaded) return;

    if (isPlaying && playMode !== "default") {
      video.play().catch((e) => console.error("Play failed", e));
    } else {
      video.pause();
    }
  }, [isPlaying, isVideoLoaded, playMode]);

  /**
   * 处理时间更新
   */
  useEffect(() => {
    const video = videoRef.current;
    if (!video || !isVideoLoaded || !currentVideo) return;

    const handleTimeUpdate = () => {
      if (playMode === "all") {
        // 成片播放模式：将视频时间转换为全局时间
        const relativeTime = video.currentTime - currentVideo.inPoint;
        const globalTime = currentVideo.startTime + relativeTime;

        // 检查是否超出当前切片的出点
        if (video.currentTime >= currentVideo.outPoint) {
          // 跳到下一个切片或停止播放
          const nextClip = videoClips.find(
            (clip) => clip.startTime >= currentVideo.endTime
          );
          if (nextClip) {
            onTimeUpdate(nextClip.startTime);
            onClipActivate?.(nextClip.id);
          } else {
            // 没有下一个切片，停止播放
            onPlay();
          }
          return;
        }

        onTimeUpdate(globalTime);
      } else if (playMode === "single") {
        // 单片播放模式：检查是否超出出点
        if (video.currentTime >= currentVideo.outPoint) {
          video.pause();
          onPlay(); // 停止播放
          return;
        }

        // 更新时间轴到对应的全局时间
        const relativeTime = video.currentTime - currentVideo.inPoint;
        const globalTime = currentVideo.startTime + relativeTime;
        onTimeUpdate(globalTime);
      }
    };

    video.addEventListener("timeupdate", handleTimeUpdate);
    return () => video.removeEventListener("timeupdate", handleTimeUpdate);
  }, [
    isVideoLoaded,
    currentVideo,
    playMode,
    onTimeUpdate,
    onPlay,
    videoClips,
    onClipActivate,
  ]);

  return (
    <div
      className="relative flex items-center justify-center w-full bg-black"
      style={{ aspectRatio: "9/16", minHeight: "400px" }}
    >
      {/* 视频元素 */}
      <video
        ref={videoRef}
        className="w-full h-full object-contain"
        crossOrigin="anonymous"
        preload="metadata"
        playsInline
        muted
      />

      {/* 加载状态 */}
      {!isVideoLoaded && !loadingError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 text-white">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p>加载中...</p>
          </div>
        </div>
      )}

      {/* 错误状态 */}
      {loadingError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 text-white">
          <div className="text-center">
            <p className="text-red-400 mb-2">⚠️ {loadingError}</p>
            <p className="text-sm text-gray-400">请检查视频文件是否可用</p>
          </div>
        </div>
      )}

      {/* 无视频状态 */}
      {!currentVideo && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 text-white">
          <div className="text-center">
            <p className="text-gray-400">暂无视频</p>
          </div>
        </div>
      )}
    </div>
  );
}
