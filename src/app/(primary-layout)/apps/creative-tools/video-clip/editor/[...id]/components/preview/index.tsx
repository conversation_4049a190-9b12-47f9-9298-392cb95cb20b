"use client";

import { useState, useRef, useEffect, useMemo, useCallback } from "react";
import { Play, Pause, Maximize, Minimize } from "lucide-react";
import { useEditorStore } from "../../stores/editor";
import { useTimelineStore } from "../../stores/timeline";
import { Popover } from "antd";
import { Player } from "./player";
import { formatSecondsToTime } from "../../utils";

// 类型定义
export type ComposedTimelineItem = {
  clipId: string;
  videoUrl: string;
  inPoint: number;
  outPoint: number;
  duration: number;
  globalStart: number;
  globalEnd: number;
};

export function Preview() {
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSpeedPopover, setShowSpeedPopover] = useState(false);
  const [playMode, setPlayMode] = useState<"composed" | "single">("composed");
  const [showControls, setShowControls] = useState(true);

  const fullscreenContainerRef = useRef<HTMLDivElement>(null);
  const hideControlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { videoClips, activeClip, setActiveClip, selectedClips } =
    useEditorStore();
  const { currentTime, setCurrentTime, duration, scrollToTime } =
    useTimelineStore();

  // 自动隐藏控制面板逻辑
  const resetHideTimer = useCallback(() => {
    if (hideControlsTimeoutRef.current) {
      clearTimeout(hideControlsTimeoutRef.current);
    }

    if (isFullscreen) {
      setShowControls(true);
      hideControlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 2000); // 2秒后隐藏
    }
  }, [isFullscreen]);

  // 鼠标移动事件处理
  const handleMouseMove = useCallback(() => {
    if (isFullscreen) {
      setShowControls(true);
      resetHideTimer();
    }
  }, [isFullscreen, resetHideTimer]);

  // 鼠标进入视频区域
  const handleMouseEnter = useCallback(() => {
    if (isFullscreen) {
      setShowControls(true);
      resetHideTimer();
    }
  }, [isFullscreen, resetHideTimer]);

  // 鼠标离开视频区域
  const handleMouseLeave = useCallback(() => {
    if (isFullscreen) {
      resetHideTimer();
    }
  }, [isFullscreen, resetHideTimer]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (hideControlsTimeoutRef.current) {
        clearTimeout(hideControlsTimeoutRef.current);
      }
    };
  }, []);

  // 全屏状态变化时重置控制面板状态
  useEffect(() => {
    if (isFullscreen) {
      setShowControls(true);
      resetHideTimer();
    } else {
      setShowControls(true);
      if (hideControlsTimeoutRef.current) {
        clearTimeout(hideControlsTimeoutRef.current);
      }
    }
  }, [isFullscreen, resetHideTimer]);

  // 创建成片时间轴
  const composedTimeline = useMemo((): ComposedTimelineItem[] => {
    if (!videoClips || videoClips.length === 0) return [];

    return videoClips.map((clip) => {
      const clipDuration = clip.outPoint - clip.inPoint;
      const item: ComposedTimelineItem = {
        clipId: clip.id,
        videoUrl: clip.videoUrl,
        inPoint: clip.inPoint,
        outPoint: clip.outPoint,
        duration: clipDuration,
        globalStart: clip.startTime, // 使用 clip.startTime 作为全局开始时间
        globalEnd: clip.endTime, // 使用 clip.endTime 作为全局结束时间
      };
      return item;
    });
  }, [videoClips]);

  // 计算总时长
  const totalDuration = useMemo(() => {
    if (composedTimeline.length > 0) {
      return composedTimeline[composedTimeline.length - 1].globalEnd;
    }
    return 0;
  }, [composedTimeline]);

  // 计算时间轴的总时长（与时间轴保持一致）
  const timelineDuration = useMemo(() => {
    if (videoClips && videoClips.length > 0) {
      // 找到最大的 endTime 作为总时长，与时间轴逻辑保持一致
      return Math.max(...videoClips.map((clip) => clip.endTime));
    }
    return 60; // 默认60秒，与时间轴默认值保持一致
  }, [videoClips]);

  // 获取当前应该播放的视频和时间
  const getCurrentComposedItem = () => {
    if (playMode === "single" || composedTimeline.length === 0) {
      return composedTimeline[0] || null;
    }
    return composedTimeline.find(
      (item) => currentTime >= item.globalStart && currentTime < item.globalEnd
    );
  };

  const currentComposedItem = getCurrentComposedItem();

  // 计算进度条显示的时间（与时间轴保持一致）
  const progressTime = currentTime;

  // 计算进度条总时长（与时间轴保持一致）
  const progressDuration = timelineDuration;

  // 调整时间位置
  const adjustTimePosition = (newTime: number) => {
    if (composedTimeline.length === 0) {
      setCurrentTime(newTime);
      return;
    }

    // 找到当前时间所在的片段
    const currentItem = composedTimeline.find(
      (item) => newTime >= item.globalStart && newTime < item.globalEnd
    );

    if (currentItem) {
      // 如果当前时间在某个片段范围内，检查是否在入点出点范围内
      const relativeTime = newTime - currentItem.globalStart;
      const inPoint = currentItem.inPoint;
      const outPoint = currentItem.outPoint;

      if (relativeTime < inPoint) {
        // 如果小于入点，跳到入点
        const adjustedTime = currentItem.globalStart + inPoint;
        setCurrentTime(adjustedTime);
        return;
      } else if (relativeTime > outPoint) {
        // 如果大于出点，跳到出点
        const adjustedTime = currentItem.globalStart + outPoint;
        setCurrentTime(adjustedTime);
        return;
      }
    } else {
      // 如果当前时间不在任何片段范围内，找到最近的入点
      let nearestInPoint = null;
      let minDistance = Infinity;

      for (const item of composedTimeline) {
        const inPointTime = item.globalStart + item.inPoint;
        const distance = Math.abs(newTime - inPointTime);

        if (distance < minDistance) {
          minDistance = distance;
          nearestInPoint = inPointTime;
        }
      }

      if (nearestInPoint !== null) {
        setCurrentTime(nearestInPoint);
        return;
      }
    }

    // 如果没有需要调整的，直接使用原时间
    setCurrentTime(newTime);
  };

  const handlePlay = () => {
    // 在播放前调整时间位置
    adjustTimePosition(currentTime);
    setIsPlaying(!isPlaying);
  };

  const handleTimeUpdate = (time: number) => {
    setCurrentTime(time);
  };

  const handleVideoDurationChange = (duration: number) => {
    // Handle video duration change if needed
  };

  const handleSpeedChange = (speed: number) => {
    setPlaybackSpeed(speed);
  };

  // 处理进度条点击
  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(1, x / rect.width));

    // 与时间轴保持一致：直接使用全局时间
    const newTime = percentage * timelineDuration;

    // 调整时间位置
    adjustTimePosition(newTime);
  };

  // 全屏处理
  const toggleFullscreen = async () => {
    const container = fullscreenContainerRef.current;
    if (!container) return;

    try {
      if (!isFullscreen) {
        if (container.requestFullscreen) {
          await container.requestFullscreen();
        }
      } else {
        if (document.exitFullscreen) {
          await document.exitFullscreen();
        }
      }
    } catch (error) {
      console.error("Fullscreen toggle failed:", error);
    }
  };

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!document.fullscreenElement;
      setIsFullscreen(isCurrentlyFullscreen);

      if (!isCurrentlyFullscreen) {
        setShowSpeedPopover(false);
      }
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
    };
  }, []);

  const handlePlayCurrentClip = () => {
    if (!activeClip) return;
    setPlayMode("single");
    const clip = videoClips.find((c) => c.id === activeClip.id);
    if (clip) {
      // 调整到片段的入点位置
      const inPointTime = clip.startTime + clip.inPoint;
      adjustTimePosition(inPointTime);
    }
    // if (!isPlaying) setIsPlaying(true);
  };

  const handlePlayComposed = () => {
    setPlayMode("composed");
    // 调整到第一个片段的入点位置
    if (composedTimeline.length > 0) {
      const firstItem = composedTimeline[0];
      const inPointTime = firstItem.globalStart + firstItem.inPoint;
      adjustTimePosition(inPointTime);
    } else {
      adjustTimePosition(0);
    }
    // if (!isPlaying) setIsPlaying(true);
  };

  // 处理片段激活
  const handleClipActivate = useCallback(
    (clipId: string) => {
      // 从videoClips中找到对应的片段
      const clip = videoClips.find((vc: any) => String(vc.id) === clipId);
      if (clip) {
        // 从selectedClips中找到对应的原始片段数据
        const selectedClip = selectedClips.find(
          (sc: any) => String(sc.id) === clipId
        );
        if (selectedClip) {
          setActiveClip(selectedClip);

          // 延迟滚动，确保状态更新完成
          setTimeout(() => {
            // 计算入点的全局时间位置
            const inPointTime = clip.startTime + clip.inPoint;
            scrollToTime(inPointTime);
          }, 100);
        }
      }
    },
    [videoClips, selectedClips, setActiveClip, scrollToTime]
  );

  // 控制面板是否应该显示
  const shouldShowControls = !isFullscreen || showControls;

  return (
    <div
      ref={fullscreenContainerRef}
      className={`flex flex-col ${
        isFullscreen ? "fixed inset-0 z-50 bg-black" : "h-full w-[600px]"
      }`}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 标题栏 */}
      {!isFullscreen && (
        <div className="flex items-center gap-4 justify-between h-10 px-4">
          <div className="flex items-center gap-2">
            <div className="text-sm font-bold text-gray-700">预览</div>
          </div>
        </div>
      )}

      {/* 视频播放器容器 */}
      <div className={`flex flex-col ${isFullscreen ? "h-full" : ""}`}>
        {/* 视频播放器 */}
        <div
          className={
            isFullscreen ? "flex-1 flex items-center justify-center" : ""
          }
        >
          <Player
            currentTime={currentTime}
            isPlaying={isPlaying}
            playbackSpeed={playbackSpeed}
            onPlay={handlePlay}
            onTimeUpdate={handleTimeUpdate}
            onVideoDurationChange={handleVideoDurationChange}
            videoClips={videoClips}
            composedTimeline={composedTimeline}
            playMode={playMode}
            onClipActivate={handleClipActivate}
          />
        </div>

        {/* 控制面板 */}
        <div
          className={`transition-all duration-500 ease-out ${
            isFullscreen
              ? `absolute bottom-0 left-0 right-0 z-[9998] ${
                  shouldShowControls
                    ? "opacity-100 translate-y-0"
                    : "opacity-0 translate-y-full pointer-events-none"
                }`
              : "bg-gray-100"
          } ${isFullscreen ? "p-6" : "p-4"}`}
          style={
            isFullscreen
              ? {
                  background:
                    "linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.7) 50%, rgba(0, 0, 0, 0.3) 80%, rgba(0, 0, 0, 0) 100%)",
                  backdropFilter: "blur(15px)",
                  WebkitBackdropFilter: "blur(15px)",
                }
              : {}
          }
        >
          {/* 控制按钮 */}
          <div className="relative flex items-center">
            {/* 左侧：时间显示 */}
            <div
              className={`${
                isFullscreen ? "text-base font-medium" : "text-sm"
              } absolute left-0 ${
                isFullscreen ? "text-white drop-shadow-lg" : "text-gray-600"
              } transition-all duration-300`}
            >
              {formatSecondsToTime(progressTime)} /{" "}
              {formatSecondsToTime(progressDuration)}
            </div>

            {/* 中间：播放按钮 */}
            <div className="flex-1 flex justify-center">
              <button
                onClick={handlePlay}
                className={`${
                  isFullscreen
                    ? "p-4 text-white bg-white/20 hover:bg-white/30 border-2 border-white/30 hover:border-white/50"
                    : "p-2 bg-blue-500 hover:bg-blue-600 text-white"
                } rounded-full transition-all duration-300 shadow-lg hover:shadow-xl ${
                  isFullscreen ? "backdrop-blur-sm hover:scale-105" : ""
                }`}
              >
                {isPlaying ? (
                  <Pause size={isFullscreen ? 24 : 18} />
                ) : (
                  <Play size={isFullscreen ? 24 : 18} />
                )}
              </button>
            </div>

            {/* 右侧：控制按钮组 */}
            <div className="flex items-center gap-2 absolute right-0">
              <Popover
                content={
                  <div className="flex flex-col gap-1 rounded-lg p-2">
                    {[0.5, 0.75, 1, 1.25, 1.5, 2].map((speed) => (
                      <button
                        key={speed}
                        onClick={() => {
                          handleSpeedChange(speed);
                          setShowSpeedPopover(false);
                        }}
                        className={`p-2 text-sm text-center rounded-md transition-all duration-200 ${
                          playbackSpeed === speed &&
                          "font-medium border border-white/30 bg-black/10"
                        }`}
                      >
                        {speed}x
                      </button>
                    ))}
                  </div>
                }
                trigger="click"
                placement="top"
                open={showSpeedPopover}
                onOpenChange={setShowSpeedPopover}
              >
                <button className="w-8 h-8 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200 text-center flex items-center justify-center font-medium">
                  {playbackSpeed}x
                </button>
              </Popover>
            </div>
          </div>
        </div>

        {/* 播放模式切换 */}
        <div className="flex gap-2 my-4 px-4">
          <button
            className={`px-3 py-1 text-sm rounded transition-all duration-200 ${
              playMode === "single"
                ? "border border-blue-500 bg-blue-50 text-blue-700"
                : "bg-white border border-gray-300 text-gray-600 hover:bg-gray-50"
            }`}
            onClick={handlePlayCurrentClip}
            disabled={!activeClip}
          >
            默认播放模式
          </button>
          <button
            className={`px-3 py-1 text-sm rounded transition-all duration-200 ${
              playMode === "single"
                ? "border border-blue-500 bg-blue-50 text-blue-700"
                : "bg-white border border-gray-300 text-gray-600 hover:bg-gray-50"
            }`}
            onClick={handlePlayCurrentClip}
            disabled={!activeClip}
          >
            播放当前切片
          </button>
          <button
            className={`px-3 py-1 text-sm rounded transition-all duration-200 ${
              playMode === "composed"
                ? "border border-blue-500 bg-blue-50 text-blue-700"
                : "bg-white border border-gray-300 text-gray-600 hover:bg-gray-50"
            }`}
            onClick={handlePlayComposed}
          >
            预览素材成片
          </button>
        </div>
      </div>
    </div>
  );
}
