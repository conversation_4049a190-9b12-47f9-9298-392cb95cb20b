import { ZoomIn, ZoomOut } from "lucide-react";
import { Slider } from "antd";
import { useTimelineStore } from "../../stores/timeline";
import { useCallback, useRef, useState, useEffect } from "react";

interface TimelineHeaderProps {
  storyName: string;
  activeClip: any;
  videoClips: any[];
}

export function TimelineHeader({
  storyName,
  activeClip,
}: TimelineHeaderProps) {
  const { zoomLevel, setZoomLevel } = useTimelineStore();
  
  // 添加本地状态用于实时显示
  const [localZoomLevel, setLocalZoomLevel] = useState(zoomLevel);
  const debounceRef = useRef<NodeJS.Timeout | null>(null);
  const isDraggingRef = useRef(false);

  /**
   * 防抖更新缩放级别
   */
  const debouncedSetZoomLevel = useCallback((value: number) => {
    // 立即更新本地状态用于显示
    setLocalZoomLevel(value);
    
    // 清除之前的定时器
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }
    
    // 设置新的定时器，延迟更新全局状态
    debounceRef.current = setTimeout(() => {
      setZoomLevel(value);
    }, 150); // 150ms 防抖延迟，比之前稍微长一点
  }, [setZoomLevel]);

  /**
   * 缩小时间轴
   */
  const handleZoomOut = useCallback(() => {
    const newZoomLevel = Math.max(0.25, zoomLevel - 0.25);
    setZoomLevel(newZoomLevel);
    setLocalZoomLevel(newZoomLevel);
  }, [zoomLevel, setZoomLevel]);

  /**
   * 放大时间轴
   */
  const handleZoomIn = useCallback(() => {
    const newZoomLevel = Math.min(4, zoomLevel + 0.25);
    setZoomLevel(newZoomLevel);
    setLocalZoomLevel(newZoomLevel);
  }, [zoomLevel, setZoomLevel]);

  // 同步本地状态与全局状态
  useEffect(() => {
    if (zoomLevel !== localZoomLevel && debounceRef.current === null && !isDraggingRef.current) {
      setLocalZoomLevel(zoomLevel);
    }
  }, [zoomLevel, localZoomLevel]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  return (
    <div className="flex flex-col">
      <div className="flex items-center gap-4 justify-between h-10 px-4 border-b">
        <div className="flex items-center gap-4">
          <p className="text-sm font-bold">时间轴</p>
          <div className="text-sm flex items-center text-gray-500">
            <p>选中：</p>
            <p>
              {storyName}-片段{activeClip?.indexNumber}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <ZoomOut
            className="h-4 w-4 cursor-pointer hover:text-primary transition-colors"
            onClick={handleZoomOut}
          />
          <Slider
            className="w-24"
            value={localZoomLevel}
            onChange={(value) => {
              if (isDraggingRef.current) {
                // 如果正在拖拽，立即更新本地状态
                setLocalZoomLevel(value as number);
              } else {
                // 否则使用防抖更新
                debouncedSetZoomLevel(value as number);
              }
            }}
            onAfterChange={(value) => {
              isDraggingRef.current = false;
              setZoomLevel(value as number);
              setLocalZoomLevel(value as number);
            }}
            min={0.25}
            max={4}
            step={0.25}
            tooltip={{ open: false }}
          />
          <ZoomIn
            className="h-4 w-4 cursor-pointer hover:text-primary transition-colors"
            onClick={handleZoomIn}
          />
        </div>
      </div>
    </div>
  );
}
