/**
 * 智能时间格式化
 * 根据时间值和刻度间隔自动选择最佳显示格式
 * @param seconds 时间（秒）
 * @param interval 刻度间隔
 * @returns 格式化后的时间字符串
 */
export const formatTimeWithInterval = (
  seconds: number,
  interval: number
): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  // 3层格式化逻辑
  if (hours > 0) {
    // 长视频格式: H:MM:SS
    return `${hours}:${minutes.toString().padStart(2, "0")}:${Math.floor(secs)
      .toString()
      .padStart(2, "0")}`;
  }

  if (minutes > 0) {
    // 中等视频格式: M:SS
    return `${minutes}:${Math.floor(secs).toString().padStart(2, "0")}`;
  }

  if (interval >= 1) {
    // 整秒格式: Ss
    return `${Math.floor(secs)}s`;
  }

  // 精确模式格式: S.Ds
  return `${secs.toFixed(1)}s`;
};

/**
 * 智能刻度间隔计算
 * 根据缩放级别动态计算最佳刻度间隔
 * @param zoomLevel 缩放级别
 * @param pixelsPerSecond 每秒对应的像素数
 * @returns 刻度间隔（秒）
 */
export const getTimeInterval = (
  zoomLevel: number,
  pixelsPerSecond: number
): number => {
  const adjustedPixelsPerSecond = pixelsPerSecond * zoomLevel;

  // 7个层级的自适应刻度间隔
  if (adjustedPixelsPerSecond >= 200) return 0.1; // 0.1秒间隔 - 极度放大
  if (adjustedPixelsPerSecond >= 100) return 0.5; // 0.5秒间隔 - 高度放大
  if (adjustedPixelsPerSecond >= 50) return 1; // 1秒间隔 - 正常缩放
  if (adjustedPixelsPerSecond >= 25) return 2; // 2秒间隔 - 轻度缩小
  if (adjustedPixelsPerSecond >= 12) return 5; // 5秒间隔 - 中度缩小
  if (adjustedPixelsPerSecond >= 6) return 10; // 10秒间隔 - 高度缩小
  return 30; // 30秒间隔 - 极度缩小
};

/**
 * 判断是否为主刻度
 * @param time 时间值
 * @param interval 刻度间隔
 * @returns 是否为主刻度
 */
export const isMainMarker = (time: number, interval: number): boolean => {
  const mainInterval = interval >= 1 ? Math.max(1, interval) : 1;
  return Math.abs(time % mainInterval) < 0.001; // 使用小的容差避免浮点数精度问题
};

// 格式化时间显示
export const formatSecondsToTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, "0")}:${secs
    .toString()
    .padStart(2, "0")}`;
};