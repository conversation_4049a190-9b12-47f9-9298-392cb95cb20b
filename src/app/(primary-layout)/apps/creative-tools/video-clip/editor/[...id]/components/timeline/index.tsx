import { useEffect } from "react";
import { TimelineHeader } from "./header";
import { useEditorStore } from "../../stores/editor";
import { MainContainer } from "./main";
import { VideoClipData, SubtitleClip } from "../../types";

export function Timeline() {
  const {
    selectedClips, // 选中的片段列表
    activeClip, // 当前激活的片段
    storyName, // 故事名称
    videoClips, // 视频片段列表
    setVideoClips, // 设置视频片段列表
    setSubtitleClips, // 设置字幕片段列表
  } = useEditorStore();

  /**
   * 将选中的片段转换为视频片段列表
   */
  useEffect(() => {
    if (!selectedClips || selectedClips.length === 0) {
      setVideoClips([]);
      setSubtitleClips([]);
      return;
    }

    // 将选中的片段转换为视频片段
    let currentTimeOffset = 0;
    const newClips: VideoClipData[] = selectedClips.map((clip: any, index) => {
      const fullVideoDuration = clip.episodeDuration || 0;
      const originalStartTime = clip.startTimeSeconds || 0;
      const originalEndTime = clip.endTimeSeconds || fullVideoDuration;

      const videoClip: VideoClipData = {
        id: String(clip.id) || `video_clip_${index}`,
        name: clip.script || `片段 ${index + 1}`,
        indexNumber: clip.indexNumber,
        duration: fullVideoDuration,
        startTime: currentTimeOffset,
        endTime: currentTimeOffset + fullVideoDuration,
        videoUrl: clip.episodeUrl,
        inPoint: originalStartTime,
        outPoint: originalEndTime,
        subtitles: clip.captionsList || [],
      };

      currentTimeOffset += fullVideoDuration;
      return videoClip;
    });

    setVideoClips(newClips);

    // 处理真实字幕数据
    const realSubtitles: SubtitleClip[] = [];
    let subtitleId = 1;

    newClips.forEach((clip, clipIndex) => {
      const selectedClip = selectedClips[clipIndex];
      const captionsList = selectedClip.captionsList || [];

      captionsList.forEach((caption: any) => {
        // 计算字幕在时间轴上的实际位置
        // caption.startTime 是字幕在视频内部的开始时间
        // clip.startTime 是片段在时间轴上的开始时间
        const captionStartTime = clip.startTime + caption.startTime;
        const captionEndTime = clip.startTime + caption.endTime;

        realSubtitles.push({
          id: `subtitle_${subtitleId++}`,
          text: caption.text?.replace(/\s+/g, "") || "",
          startTime: captionStartTime,
          endTime: captionEndTime,
          speaker: caption.speaker,
          translationText: caption.translationText || " ",
        });
      });
    });

    setSubtitleClips(realSubtitles);
  }, [selectedClips, setVideoClips, setSubtitleClips]);

  return (
    <div className="border-x w-full">
      {/* 标题栏 */}
      <TimelineHeader
        storyName={storyName || "未命名故事"}
        activeClip={activeClip}
        videoClips={videoClips}
      />
      {/* 时间刻度尺、视频轨道、字幕轨道 */}
      <MainContainer />
    </div>
  );
}
